# Script PowerShell Simples para Teste
Write-Host "=== Teste de Automacao Revit ===" -ForegroundColor Green
Write-Host ""

# Verificar se familia existe
$familyPath = "teste.rfa"
if (Test-Path $familyPath) {
    Write-Host "OK Familia encontrada: $familyPath" -ForegroundColor Green
} else {
    Write-Host "ERRO: Familia nao encontrada: $familyPath" -ForegroundColor Red
    Write-Host "Pressione qualquer tecla para sair..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

# Verificar .NET SDK
Write-Host "Verificando .NET SDK..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OK .NET SDK encontrado: $dotnetVersion" -ForegroundColor Green
    } else {
        Write-Host "ERRO: .NET SDK nao encontrado" -ForegroundColor Red
        Write-Host "Instale em: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
        Write-Host "Pressione qualquer tecla para sair..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit
    }
} catch {
    Write-Host "ERRO: .NET SDK nao encontrado" -ForegroundColor Red
    Write-Host "Instale em: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Write-Host "Pressione qualquer tecla para sair..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

# Verificar se projeto existe
if (Test-Path "RevitAutomation.csproj") {
    Write-Host "OK Projeto encontrado: RevitAutomation.csproj" -ForegroundColor Green
} else {
    Write-Host "ERRO: Projeto nao encontrado: RevitAutomation.csproj" -ForegroundColor Red
    Write-Host "Pressione qualquer tecla para sair..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

# Tentar compilar
Write-Host "Compilando projeto..." -ForegroundColor Yellow
try {
    $buildOutput = dotnet build "RevitAutomation.csproj" --configuration Release 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OK Compilacao bem-sucedida!" -ForegroundColor Green
    } else {
        Write-Host "ERRO na compilacao:" -ForegroundColor Red
        Write-Host $buildOutput -ForegroundColor Red
        Write-Host "Pressione qualquer tecla para sair..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit
    }
} catch {
    Write-Host "ERRO: Falha na compilacao" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host "Pressione qualquer tecla para sair..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

# Verificar se DLL foi criada
$dllPath = "bin\Release\net48\RevitAutomation.dll"
if (Test-Path $dllPath) {
    Write-Host "OK DLL criada: $dllPath" -ForegroundColor Green
} else {
    Write-Host "ERRO: DLL nao foi criada: $dllPath" -ForegroundColor Red
    Write-Host "Pressione qualquer tecla para sair..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

Write-Host ""
Write-Host "=== Todos os testes passaram! ===" -ForegroundColor Green
Write-Host "Agora execute o script completo: .\RevitAutomation.ps1" -ForegroundColor Yellow
Write-Host ""
Write-Host "Pressione qualquer tecla para continuar..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
