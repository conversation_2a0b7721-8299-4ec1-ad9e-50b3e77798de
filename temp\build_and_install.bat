@echo off
echo === Compilacao e Instalacao do Add-in Revit ===
echo.

REM Verificar se o .NET SDK esta instalado
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: .NET SDK nao encontrado. Instale o .NET SDK primeiro.
    echo Download: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo Compilando o projeto...
dotnet build RevitAutomation.csproj --configuration Release

if %errorlevel% neq 0 (
    echo ERRO: Falha na compilacao.
    pause
    exit /b 1
)

echo Compilacao concluida com sucesso!
echo.

REM Definir caminhos de instalacao do Revit
set REVIT_ADDINS_2024=%APPDATA%\Autodesk\Revit\Addins\2024
set REVIT_ADDINS_2023=%APPDATA%\Autodesk\Revit\Addins\2023
set REVIT_ADDINS_2022=%APPDATA%\Autodesk\Revit\Addins\2022

REM Verificar qual versao do Revit esta instalada e copiar arquivos
if exist "%REVIT_ADDINS_2024%" (
    echo Instalando para Revit 2024...
    if not exist "%REVIT_ADDINS_2024%" mkdir "%REVIT_ADDINS_2024%"
    copy "bin\Release\RevitAutomation.dll" "%REVIT_ADDINS_2024%\"
    copy "RevitAutomation.addin" "%REVIT_ADDINS_2024%\"
    echo Add-in instalado para Revit 2024!
) else if exist "%REVIT_ADDINS_2023%" (
    echo Instalando para Revit 2023...
    if not exist "%REVIT_ADDINS_2023%" mkdir "%REVIT_ADDINS_2023%"
    copy "bin\Release\RevitAutomation.dll" "%REVIT_ADDINS_2023%\"
    copy "RevitAutomation.addin" "%REVIT_ADDINS_2023%\"
    echo Add-in instalado para Revit 2023!
) else if exist "%REVIT_ADDINS_2022%" (
    echo Instalando para Revit 2022...
    if not exist "%REVIT_ADDINS_2022%" mkdir "%REVIT_ADDINS_2022%"
    copy "bin\Release\RevitAutomation.dll" "%REVIT_ADDINS_2022%\"
    copy "RevitAutomation.addin" "%REVIT_ADDINS_2022%\"
    echo Add-in instalado para Revit 2022!
) else (
    echo AVISO: Nenhuma pasta de add-ins do Revit encontrada.
    echo Copie manualmente os arquivos:
    echo - bin\Release\RevitAutomation.dll
    echo - RevitAutomation.addin
    echo Para: %%APPDATA%%\Autodesk\Revit\Addins\[VERSAO]\
)

echo.
echo === Instalacao Concluida ===
echo.
echo Para usar o add-in:
echo 1. Abra o Revit
echo 2. Crie um novo projeto ou abra um existente
echo 3. Va em Add-Ins ^> External Tools ^> Automatizar Modelo Revit
echo 4. O add-in ira:
echo    - Carregar a familia teste.rfa
echo    - Inserir uma instancia no modelo
echo    - Salvar o modelo como modelo_automatico.rvt
echo    - Exportar para modelo_automatico.ifc
echo.
pause
