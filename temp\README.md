# Automação Revit - Criação de Modelo e Exportação IFC

Este projeto automatiza completamente o processo de:
1. Criar um novo modelo Revit
2. Carregar e inserir a família `teste.rfa`
3. Salvar o modelo como `modelo_automatico.rvt`
4. Exportar para formato IFC como `modelo_automatico.ifc`

## Arquivos Incluídos

- **RevitAutomation.cs** - Código principal do add-in em C#
- **RevitAutomation.csproj** - Arquivo de projeto para compilação
- **RevitAutomation.addin** - Manifesto do add-in para o Revit
- **build_and_install.bat** - Script batch para compilar e instalar
- **RevitAutomation.ps1** - Script PowerShell para automação completa
- **revit_automation.py** - Script Python alternativo
- **teste.rfa** - Família Revit a ser inserida

## Pré-requisitos

1. **Autodesk Revit** (2021, 2022, 2023 ou 2024)
2. **.NET SDK** (versão 4.8 ou superior)
3. **Visual Studio** ou **Visual Studio Build Tools** (opcional, para compilação manual)

## Métodos de Execução

### Método 1: Script PowerShell (Recomendado)

```powershell
# Executar como administrador
.\RevitAutomation.ps1
```

Este script:
- Verifica todos os pré-requisitos
- Compila automaticamente o add-in
- Instala o add-in no Revit
- Inicia o Revit
- Fornece instruções passo a passo

### Método 2: Script Batch

```batch
# Executar como administrador
build_and_install.bat
```

### Método 3: Compilação Manual

```bash
# Compilar o projeto
dotnet build RevitAutomation.csproj --configuration Release

# Copiar arquivos para pasta de add-ins do Revit
# Exemplo para Revit 2024:
copy bin\Release\RevitAutomation.dll %APPDATA%\Autodesk\Revit\Addins\2024\
copy RevitAutomation.addin %APPDATA%\Autodesk\Revit\Addins\2024\
```

## Como Usar

### Automático (após instalação do add-in):

1. Abra o Revit
2. Crie um novo projeto (File > New > Project)
3. Vá em **Add-Ins** > **External Tools** > **"Automatizar Modelo Revit"**
4. O processo será executado automaticamente

### Manual (usando API):

1. Execute o script Python: `python revit_automation.py`
2. Siga as instruções na tela

## Arquivos de Saída

Após a execução bem-sucedida, os seguintes arquivos serão criados:

- **modelo_automatico.rvt** - Modelo Revit com a família inserida
- **modelo_automatico.ifc** - Arquivo IFC exportado

## Configurações de Exportação IFC

O código está configurado para exportar IFC com as seguintes opções:
- Versão: IFC 2x3 CV2
- Quantidades base: Habilitado
- Propriedades comuns IFC: Habilitado
- Nível de tessellação: 0.5
- GUID IFC: Preservado

## Solução de Problemas

### Erro: "Família não encontrada"
- Verifique se o arquivo `teste.rfa` existe no diretório atual
- Certifique-se de que o arquivo não está corrompido

### Erro: "Revit não encontrado"
- Verifique se o Revit está instalado corretamente
- Atualize os caminhos no código se necessário

### Erro: ".NET SDK não encontrado"
- Instale o .NET SDK: https://dotnet.microsoft.com/download
- Reinicie o prompt de comando após a instalação

### Erro: "Falha na compilação"
- Verifique se as referências do Revit API estão corretas
- Atualize os caminhos no arquivo .csproj conforme sua instalação

### Add-in não aparece no Revit
- Verifique se os arquivos foram copiados para a pasta correta:
  - `%APPDATA%\Autodesk\Revit\Addins\[VERSÃO]\`
- Reinicie o Revit após instalar o add-in
- Verifique se não há erros no arquivo .addin

## Personalização

### Alterar ponto de inserção:
Modifique a linha no código C#:
```csharp
XYZ insertionPoint = new XYZ(0, 0, 0); // X, Y, Z em unidades do modelo
```

### Alterar configurações IFC:
Modifique o método `ExportToIFC()` no arquivo `RevitAutomation.cs`

### Usar família diferente:
- Substitua o arquivo `teste.rfa` pela família desejada
- Ou modifique a variável `familyPath` no código

## Estrutura do Código

```
RevitAutomation/
├── RevitAutomation.cs      # Lógica principal do add-in
├── RevitAutomation.csproj  # Configuração do projeto
├── RevitAutomation.addin   # Manifesto do add-in
├── build_and_install.bat  # Script de compilação/instalação
├── RevitAutomation.ps1     # Automação PowerShell
├── revit_automation.py     # Script Python alternativo
├── teste.rfa              # Família Revit de exemplo
└── README.md              # Este arquivo
```

## Licença

Este código é fornecido como exemplo educacional. Certifique-se de ter as licenças apropriadas do Autodesk Revit para uso comercial.

## Suporte

Para problemas específicos:
1. Verifique os logs do Revit em `%APPDATA%\Autodesk\Revit\[VERSÃO]\Journals\`
2. Execute o Revit com `/nosplash /language ENU` para melhor debugging
3. Verifique a compatibilidade da versão do .NET Framework
