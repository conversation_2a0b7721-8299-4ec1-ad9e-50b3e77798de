import os
import sys
import subprocess
import time
import clr
from pathlib import Path

def setup_revit_api():
    """Configura o ambiente para usar a API do Revit"""
    # Caminhos típicos do Revit (ajuste conforme sua instalação)
    revit_paths = [
        r"C:\Program Files\Autodesk\Revit 2024",
        r"C:\Program Files\Autodesk\Revit 2023", 
        r"C:\Program Files\Autodesk\Revit 2022",
        r"C:\Program Files\Autodesk\Revit 2021"
    ]
    
    revit_path = None
    for path in revit_paths:
        if os.path.exists(path):
            revit_path = path
            break
    
    if not revit_path:
        raise Exception("Revit não encontrado. Verifique a instalação.")
    
    # Adicionar referências necessárias
    sys.path.append(revit_path)
    
    try:
        clr.AddReference("RevitAPI")
        clr.AddReference("RevitAPIUI")
        clr.AddReference("RevitServices")
        clr.AddReference("RevitNodes")
        
        # Importar namespaces do Revit
        import Autodesk.Revit.DB as DB
        import Autodesk.Revit.UI as UI
        import Autodesk.Revit.ApplicationServices as AS
        
        return DB, UI, AS, revit_path
        
    except Exception as e:
        print(f"Erro ao carregar APIs do Revit: {e}")
        return None, None, None, revit_path

def create_revit_script():
    """Cria um script Dynamo para automatizar o processo"""
    script_content = '''
import clr
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')
clr.AddReference('RevitServices')
clr.AddReference('RevitNodes')

import Autodesk.Revit.DB as DB
import Autodesk.Revit.UI as UI
from Autodesk.Revit.DB import *
from RevitServices.Persistence import DocumentManager
from RevitServices.Transactions import TransactionManager
import System
import os

# Obter documento ativo
doc = DocumentManager.Instance.CurrentDBDocument
uiapp = DocumentManager.Instance.CurrentUIApplication
app = uiapp.Application

# Caminhos
current_dir = os.getcwd()
family_path = os.path.join(current_dir, "teste.rfa")
output_path = os.path.join(current_dir, "modelo_automatico.rvt")
ifc_output_path = os.path.join(current_dir, "modelo_automatico.ifc")

# Verificar se família existe
if not os.path.exists(family_path):
    OUT = f"Família não encontrada: {family_path}"
else:
    try:
        # Iniciar transação
        TransactionManager.Instance.EnsureInTransaction(doc)
        
        # Carregar família
        family = None
        if doc.LoadFamily(family_path, family):
            # Obter símbolo da família
            family_symbols = list(family.GetFamilySymbolIds())
            if family_symbols:
                family_symbol = doc.GetElement(family_symbols[0])
                
                # Ativar símbolo se necessário
                if not family_symbol.IsActive:
                    family_symbol.Activate()
                
                # Inserir instância na origem
                insertion_point = XYZ(0, 0, 0)
                instance = doc.Create.NewFamilyInstance(
                    insertion_point,
                    family_symbol,
                    StructuralType.NonStructural
                )
                
                if instance:
                    OUT = "Família inserida com sucesso!"
                else:
                    OUT = "Falha ao inserir família"
            else:
                OUT = "Nenhum símbolo encontrado na família"
        else:
            OUT = "Falha ao carregar família"
        
        # Finalizar transação
        TransactionManager.Instance.TransactionTaskDone()
        
    except Exception as e:
        OUT = f"Erro: {str(e)}"
        TransactionManager.Instance.TransactionTaskDone()
'''
    
    with open("temp/revit_automation_script.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    return "temp/revit_automation_script.py"

def run_revit_automation():
    """Executa a automação do Revit"""
    try:
        # Verificar se a família existe
        family_path = "teste.rfa"
        if not os.path.exists(family_path):
            print(f"Erro: Família não encontrada: {family_path}")
            return False
        
        print("Configurando ambiente Revit...")
        DB, UI, AS, revit_path = setup_revit_api()
        
        if not DB:
            print("Erro: Não foi possível configurar a API do Revit")
            return False
        
        print("Criando script de automação...")
        script_path = create_revit_script()
        
        # Criar arquivo de comando para o Revit
        revit_exe = os.path.join(revit_path, "Revit.exe")
        
        if not os.path.exists(revit_exe):
            print(f"Erro: Executável do Revit não encontrado: {revit_exe}")
            return False
        
        print("Iniciando Revit...")
        
        # Comando para abrir Revit com um novo projeto
        cmd = [
            revit_exe,
            "/nosplash",
            "/language", "ENU"
        ]
        
        print(f"Executando: {' '.join(cmd)}")
        
        # Executar Revit
        process = subprocess.Popen(cmd, cwd=os.getcwd())
        
        print("Revit iniciado. Aguarde o processamento...")
        print("IMPORTANTE: Execute manualmente o comando de automação no Revit")
        print("ou use o add-in RevitAutomation.dll após compilar o código C#")
        
        return True
        
    except Exception as e:
        print(f"Erro na automação: {e}")
        return False

if __name__ == "__main__":
    print("=== Automação Revit ===")
    print("Este script automatiza:")
    print("1. Criação de modelo Revit")
    print("2. Inserção da família teste.rfa")
    print("3. Exportação para IFC")
    print()
    
    success = run_revit_automation()
    
    if success:
        print("\nScript executado com sucesso!")
        print("Para automação completa, compile o código C# como add-in do Revit.")
    else:
        print("\nFalha na execução do script.")
