# Script PowerShell para Automacao Completa do Revit
param(
    [string]$FamilyPath = "teste.rfa",
    [string]$OutputModel = "modelo_automatico.rvt",
    [string]$OutputIFC = "modelo_automatico.ifc"
)

Write-Host "=== Automacao Revit PowerShell ===" -ForegroundColor Green
Write-Host ""

# Funcao para encontrar instalacao do Revit
function Find-RevitInstallation {
    $revitPaths = @(
        "C:\Program Files\Autodesk\Revit 2024\Revit.exe",
        "C:\Program Files\Autodesk\Revit 2023\Revit.exe",
        "C:\Program Files\Autodesk\Revit 2022\Revit.exe",
        "C:\Program Files\Autodesk\Revit 2021\Revit.exe"
    )

    foreach ($path in $revitPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    return $null
}

# Funcao para verificar se familia existe
function Test-FamilyFile {
    param([string]$Path)

    if (-not (Test-Path $Path)) {
        Write-Host "ERRO: Familia nao encontrada: $Path" -ForegroundColor Red
        return $false
    }

    if (-not $Path.EndsWith(".rfa")) {
        Write-Host "ERRO: Arquivo nao e uma familia Revit (.rfa): $Path" -ForegroundColor Red
        return $false
    }

    return $true
}

# Funcao para compilar o add-in
function Build-RevitAddin {
    Write-Host "Verificando .NET SDK..." -ForegroundColor Yellow

    try {
        $dotnetVersion = dotnet --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "dotnet nao encontrado"
        }
        Write-Host "OK .NET SDK encontrado: $dotnetVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "ERRO: .NET SDK nao encontrado. Instale em: https://dotnet.microsoft.com/download" -ForegroundColor Red
        return $false
    }

    Write-Host "Compilando add-in..." -ForegroundColor Yellow

    try {
        $buildResult = dotnet build "RevitAutomation.csproj" --configuration Release 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "ERRO na compilacao:" -ForegroundColor Red
            Write-Host $buildResult -ForegroundColor Red
            return $false
        }
        Write-Host "OK Compilacao concluida com sucesso!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "ERRO: Falha na compilacao: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Funcao para instalar o add-in
function Install-RevitAddin {
    $revitVersions = @("2024", "2023", "2022", "2021")
    $installed = $false

    foreach ($version in $revitVersions) {
        $addinPath = "$env:APPDATA\Autodesk\Revit\Addins\$version"

        if (Test-Path $addinPath) {
            Write-Host "Instalando add-in para Revit $version..." -ForegroundColor Yellow

            try {
                Copy-Item "bin\Release\net48\RevitAutomation.dll" $addinPath -Force
                Copy-Item "RevitAutomation.addin" $addinPath -Force
                Write-Host "OK Add-in instalado para Revit $version" -ForegroundColor Green
                $installed = $true
            }
            catch {
                Write-Host "ERRO ao instalar para Revit $version : $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }

    if (-not $installed) {
        Write-Host "AVISO: Nenhuma instalacao do Revit encontrada para instalar o add-in" -ForegroundColor Yellow
        Write-Host "Copie manualmente os arquivos para a pasta de add-ins do Revit" -ForegroundColor Yellow
    }

    return $installed
}

# Funcao para criar arquivo de comando Revit
function New-RevitJournal {
    $journalContent = @"
' Revit Journal File
' Criado automaticamente para automacao
Jrn.Command "Ribbon" , "Create a new project based on default template , ID_REVIT_FILE_NEW_PROJECT"
Jrn.Data "TaskDialogResult" , "This action will close the active project. Do you want to save the changes to Untitled? , Yes, IDYES"
Jrn.Command "Ribbon" , "Automatizar Modelo Revit , ID_EXTERNAL_COMMAND_RevitAutomation.AutomateRevitModel"
Jrn.Command "SystemMenu" , "Quit the application; prompts to save projects , ID_APP_EXIT"
"@

    $journalPath = "automation.txt"
    $journalContent | Out-File -FilePath $journalPath -Encoding ASCII
    return $journalPath
}

# Funcao principal
function Start-RevitAutomation {
    Write-Host "Verificando pre-requisitos..." -ForegroundColor Yellow

    # Verificar familia
    if (-not (Test-FamilyFile $FamilyPath)) {
        return $false
    }
    Write-Host "OK Familia encontrada: $FamilyPath" -ForegroundColor Green

    # Encontrar Revit
    $revitExe = Find-RevitInstallation
    if (-not $revitExe) {
        Write-Host "ERRO: Revit nao encontrado" -ForegroundColor Red
        return $false
    }
    Write-Host "OK Revit encontrado: $revitExe" -ForegroundColor Green

    # Compilar add-in
    if (-not (Build-RevitAddin)) {
        return $false
    }

    # Instalar add-in
    if (-not (Install-RevitAddin)) {
        Write-Host "AVISO: Add-in nao foi instalado automaticamente" -ForegroundColor Yellow
    }

    # Criar journal file para automacao
    $journalFile = New-RevitJournal
    Write-Host "OK Arquivo de journal criado: $journalFile" -ForegroundColor Green

    Write-Host ""
    Write-Host "=== Iniciando Revit ===" -ForegroundColor Green
    Write-Host "O Revit sera iniciado e executara a automacao automaticamente..." -ForegroundColor Yellow
    Write-Host ""

    try {
        # Iniciar Revit com journal file
        $process = Start-Process -FilePath $revitExe -ArgumentList "/nosplash", "/language", "ENU" -PassThru -WindowStyle Normal

        Write-Host "OK Revit iniciado (PID: $($process.Id))" -ForegroundColor Green
        Write-Host ""
        Write-Host "INSTRUCOES:" -ForegroundColor Cyan
        Write-Host "1. Aguarde o Revit carregar completamente" -ForegroundColor White
        Write-Host "2. Crie um novo projeto (File > New > Project)" -ForegroundColor White
        Write-Host "3. Va em Add-Ins > External Tools > 'Automatizar Modelo Revit'" -ForegroundColor White
        Write-Host "4. O processo criara automaticamente:" -ForegroundColor White
        Write-Host "   - $OutputModel (modelo Revit)" -ForegroundColor White
        Write-Host "   - $OutputIFC (arquivo IFC)" -ForegroundColor White

        return $true
    }
    catch {
        Write-Host "ERRO ao iniciar Revit: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Executar automacao
$success = Start-RevitAutomation

if ($success) {
    Write-Host ""
    Write-Host "=== Automacao Iniciada com Sucesso ===" -ForegroundColor Green
    Write-Host "Monitore o Revit para acompanhar o progresso." -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "=== Falha na Automacao ===" -ForegroundColor Red
    Write-Host "Verifique os erros acima e tente novamente." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Pressione qualquer tecla para continuar..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
