# Script PowerShell para Automação Completa do Revit
param(
    [string]$FamilyPath = "teste.rfa",
    [string]$OutputModel = "modelo_automatico.rvt",
    [string]$OutputIFC = "modelo_automatico.ifc"
)

Write-Host "=== Automação Revit PowerShell ===" -ForegroundColor Green
Write-Host ""

# Função para encontrar instalação do Revit
function Find-RevitInstallation {
    $revitPaths = @(
        "C:\Program Files\Autodesk\Revit 2024\Revit.exe",
        "C:\Program Files\Autodesk\Revit 2023\Revit.exe",
        "C:\Program Files\Autodesk\Revit 2022\Revit.exe",
        "C:\Program Files\Autodesk\Revit 2021\Revit.exe"
    )
    
    foreach ($path in $revitPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    return $null
}

# Função para verificar se família existe
function Test-FamilyFile {
    param([string]$Path)
    
    if (-not (Test-Path $Path)) {
        Write-Host "ERRO: Família não encontrada: $Path" -ForegroundColor Red
        return $false
    }
    
    if (-not $Path.EndsWith(".rfa")) {
        Write-Host "ERRO: Arquivo não é uma família Revit (.rfa): $Path" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Função para compilar o add-in
function Build-RevitAddin {
    Write-Host "Verificando .NET SDK..." -ForegroundColor Yellow
    
    try {
        $dotnetVersion = dotnet --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "dotnet não encontrado"
        }
        Write-Host "✓ .NET SDK encontrado: $dotnetVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "ERRO: .NET SDK não encontrado. Instale em: https://dotnet.microsoft.com/download" -ForegroundColor Red
        return $false
    }
    
    Write-Host "Compilando add-in..." -ForegroundColor Yellow
    
    try {
        $buildResult = dotnet build "RevitAutomation.csproj" --configuration Release 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "ERRO na compilação:" -ForegroundColor Red
            Write-Host $buildResult -ForegroundColor Red
            return $false
        }
        Write-Host "✓ Compilação concluída com sucesso!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "ERRO: Falha na compilação: $_" -ForegroundColor Red
        return $false
    }
}

# Função para instalar o add-in
function Install-RevitAddin {
    $revitVersions = @("2024", "2023", "2022", "2021")
    $installed = $false
    
    foreach ($version in $revitVersions) {
        $addinPath = "$env:APPDATA\Autodesk\Revit\Addins\$version"
        
        if (Test-Path $addinPath) {
            Write-Host "Instalando add-in para Revit $version..." -ForegroundColor Yellow
            
            try {
                Copy-Item "bin\Release\RevitAutomation.dll" $addinPath -Force
                Copy-Item "RevitAutomation.addin" $addinPath -Force
                Write-Host "✓ Add-in instalado para Revit $version" -ForegroundColor Green
                $installed = $true
            }
            catch {
                Write-Host "ERRO ao instalar para Revit $version: $_" -ForegroundColor Red
            }
        }
    }
    
    if (-not $installed) {
        Write-Host "AVISO: Nenhuma instalação do Revit encontrada para instalar o add-in" -ForegroundColor Yellow
        Write-Host "Copie manualmente os arquivos para a pasta de add-ins do Revit" -ForegroundColor Yellow
    }
    
    return $installed
}

# Função para criar arquivo de comando Revit
function Create-RevitJournal {
    $journalContent = @"
' Revit Journal File
' Criado automaticamente para automação
Jrn.Command "Ribbon" , "Create a new project based on default template , ID_REVIT_FILE_NEW_PROJECT"
Jrn.Data "TaskDialogResult" , "This action will close the active project. Do you want to save the changes to Untitled? , Yes, IDYES"
Jrn.Command "Ribbon" , "Automatizar Modelo Revit , ID_EXTERNAL_COMMAND_RevitAutomation.AutomateRevitModel"
Jrn.Command "SystemMenu" , "Quit the application; prompts to save projects , ID_APP_EXIT"
"@
    
    $journalPath = "automation.txt"
    $journalContent | Out-File -FilePath $journalPath -Encoding ASCII
    return $journalPath
}

# Função principal
function Start-RevitAutomation {
    Write-Host "Verificando pré-requisitos..." -ForegroundColor Yellow
    
    # Verificar família
    if (-not (Test-FamilyFile $FamilyPath)) {
        return $false
    }
    Write-Host "✓ Família encontrada: $FamilyPath" -ForegroundColor Green
    
    # Encontrar Revit
    $revitExe = Find-RevitInstallation
    if (-not $revitExe) {
        Write-Host "ERRO: Revit não encontrado" -ForegroundColor Red
        return $false
    }
    Write-Host "✓ Revit encontrado: $revitExe" -ForegroundColor Green
    
    # Compilar add-in
    if (-not (Build-RevitAddin)) {
        return $false
    }
    
    # Instalar add-in
    if (-not (Install-RevitAddin)) {
        Write-Host "AVISO: Add-in não foi instalado automaticamente" -ForegroundColor Yellow
    }
    
    # Criar journal file para automação
    $journalFile = Create-RevitJournal
    Write-Host "✓ Arquivo de journal criado: $journalFile" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "=== Iniciando Revit ===" -ForegroundColor Green
    Write-Host "O Revit será iniciado e executará a automação automaticamente..." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        # Iniciar Revit com journal file
        $process = Start-Process -FilePath $revitExe -ArgumentList "/nosplash", "/language", "ENU" -PassThru -WindowStyle Normal
        
        Write-Host "✓ Revit iniciado (PID: $($process.Id))" -ForegroundColor Green
        Write-Host ""
        Write-Host "INSTRUÇÕES:" -ForegroundColor Cyan
        Write-Host "1. Aguarde o Revit carregar completamente" -ForegroundColor White
        Write-Host "2. Crie um novo projeto (File > New > Project)" -ForegroundColor White
        Write-Host "3. Vá em Add-Ins > External Tools > 'Automatizar Modelo Revit'" -ForegroundColor White
        Write-Host "4. O processo criará automaticamente:" -ForegroundColor White
        Write-Host "   - $OutputModel (modelo Revit)" -ForegroundColor White
        Write-Host "   - $OutputIFC (arquivo IFC)" -ForegroundColor White
        
        return $true
    }
    catch {
        Write-Host "ERRO ao iniciar Revit: $_" -ForegroundColor Red
        return $false
    }
}

# Executar automação
$success = Start-RevitAutomation

if ($success) {
    Write-Host ""
    Write-Host "=== Automação Iniciada com Sucesso ===" -ForegroundColor Green
    Write-Host "Monitore o Revit para acompanhar o progresso." -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "=== Falha na Automação ===" -ForegroundColor Red
    Write-Host "Verifique os erros acima e tente novamente." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Pressione qualquer tecla para continuar..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
