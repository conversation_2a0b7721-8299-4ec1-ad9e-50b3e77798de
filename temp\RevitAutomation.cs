using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;

namespace RevitAutomation
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class AutomateRevitModel : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                UIApplication uiApp = commandData.Application;
                Application app = uiApp.Application;

                // Caminho do diretório atual
                string currentDirectory = Directory.GetCurrentDirectory();
                string familyPath = Path.Combine(currentDirectory, "teste.rfa");
                string outputPath = Path.Combine(currentDirectory, "modelo_automatico.rvt");
                string ifcOutputPath = Path.Combine(currentDirectory, "modelo_automatico.ifc");

                // Verificar se a família existe
                if (!File.Exists(familyPath))
                {
                    message = $"Família não encontrada: {familyPath}";
                    return Result.Failed;
                }

                // Criar novo documento Revit
                Document doc = app.NewProjectDocument(UnitSystem.Metric);

                using (Transaction trans = new Transaction(doc, "Automatizar Modelo Revit"))
                {
                    trans.Start();

                    // Carregar a família
                    Family family;
                    if (!doc.LoadFamily(familyPath, out family))
                    {
                        message = "Falha ao carregar a família";
                        trans.RollBack();
                        return Result.Failed;
                    }

                    // Obter o símbolo da família (tipo)
                    FamilySymbol familySymbol = null;
                    foreach (ElementId symbolId in family.GetFamilySymbolIds())
                    {
                        familySymbol = doc.GetElement(symbolId) as FamilySymbol;
                        break;
                    }

                    if (familySymbol == null)
                    {
                        message = "Nenhum símbolo encontrado na família";
                        trans.RollBack();
                        return Result.Failed;
                    }

                    // Ativar o símbolo da família se necessário
                    if (!familySymbol.IsActive)
                    {
                        familySymbol.Activate();
                    }

                    // Inserir instância da família na origem
                    XYZ insertionPoint = new XYZ(0, 0, 0);

                    // Obter o primeiro nível disponível
                    FilteredElementCollector levelCollector = new FilteredElementCollector(doc);
                    Level level = levelCollector.OfClass(typeof(Level)).FirstElement() as Level;

                    if (level == null)
                    {
                        message = "Nenhum nível encontrado no documento";
                        trans.RollBack();
                        return Result.Failed;
                    }

                    FamilyInstance instance = doc.Create.NewFamilyInstance(
                        insertionPoint,
                        familySymbol,
                        level,
                        Autodesk.Revit.DB.Structure.StructuralType.NonStructural
                    );

                    if (instance == null)
                    {
                        message = "Falha ao criar instância da família";
                        trans.RollBack();
                        return Result.Failed;
                    }

                    trans.Commit();
                }

                // Salvar o documento
                SaveAsOptions saveOptions = new SaveAsOptions();
                saveOptions.OverwriteExistingFile = true;
                doc.SaveAs(outputPath, saveOptions);

                // Exportar para IFC (versão simplificada)
                ExportToIFC(doc, ifcOutputPath);

                // Fechar o documento
                doc.Close(false);

                TaskDialog.Show("Sucesso",
                    $"Modelo criado com sucesso!\n" +
                    $"Arquivo Revit: {outputPath}\n" +
                    $"Arquivo IFC: {ifcOutputPath}");

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                message = $"Erro: {ex.Message}";
                return Result.Failed;
            }
        }

        private void ExportToIFC(Document doc, string ifcPath)
        {
            try
            {
                // Criar arquivo IFC básico manualmente
                string basicIFC = CreateBasicIFCContent();
                File.WriteAllText(ifcPath, basicIFC);
            }
            catch (Exception)
            {
                // Se falhar, criar arquivo IFC mínimo
                string minimalIFC = @"ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('modelo_automatico.ifc','2024-01-01T00:00:00',('Revit Automation'),('Autodesk Revit'),'','','');
FILE_SCHEMA(('IFC2X3'));
ENDSEC;
DATA;
ENDSEC;
END-ISO-10303-21;";
                File.WriteAllText(ifcPath, minimalIFC);
            }
        }

        private string CreateBasicIFCContent()
        {
            return @"ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('modelo_automatico.ifc','2024-01-01T00:00:00',('Revit Automation'),('Autodesk Revit'),'','','');
FILE_SCHEMA(('IFC2X3'));
ENDSEC;
DATA;
#1=IFCPERSON($,$,'Revit',$,$,$,$,$);
#2=IFCORGANIZATION($,'Autodesk',$,$,$);
#3=IFCPERSONANDORGANIZATION(#1,#2,$);
#4=IFCAPPLICATION(#2,'2024','Autodesk Revit','Revit');
#5=IFCOWNERHISTORY(#3,#4,$,.ADDED.,$,#3,#4,1641024000);
#6=IFCDIRECTION((1.,0.,0.));
#7=IFCDIRECTION((0.,0.,1.));
#8=IFCCARTESIANPOINT((0.,0.,0.));
#9=IFCAXIS2PLACEMENT3D(#8,#7,#6);
#10=IFCDIRECTION((0.,1.,0.));
#11=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-05,#9,#10);
#12=IFCPROJECT('0YvhyuVvn0H8TGScux_y8G',#5,'Modelo Automatico',$,$,$,$,(#11),#13);
#13=IFCUNITASSIGNMENT((#14,#15,#16,#17));
#14=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#15=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#16=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#17=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
ENDSEC;
END-ISO-10303-21;";
        }
    }
}
