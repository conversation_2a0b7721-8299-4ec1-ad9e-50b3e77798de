using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB.IFC;

namespace RevitAutomation
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class AutomateRevitModel : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                UIApplication uiApp = commandData.Application;
                Application app = uiApp.Application;
                
                // Caminho do diretório atual
                string currentDirectory = Directory.GetCurrentDirectory();
                string familyPath = Path.Combine(currentDirectory, "teste.rfa");
                string outputPath = Path.Combine(currentDirectory, "modelo_automatico.rvt");
                string ifcOutputPath = Path.Combine(currentDirectory, "modelo_automatico.ifc");
                
                // Verificar se a família existe
                if (!File.Exists(familyPath))
                {
                    message = $"Família não encontrada: {familyPath}";
                    return Result.Failed;
                }
                
                // Criar novo documento Revit
                Document doc = app.NewProjectDocument(UnitSystem.Metric);
                
                using (Transaction trans = new Transaction(doc, "Automatizar Modelo Revit"))
                {
                    trans.Start();
                    
                    // Carregar a família
                    Family family;
                    if (!doc.LoadFamily(familyPath, out family))
                    {
                        message = "Falha ao carregar a família";
                        trans.RollBack();
                        return Result.Failed;
                    }
                    
                    // Obter o símbolo da família (tipo)
                    FamilySymbol familySymbol = null;
                    foreach (ElementId symbolId in family.GetFamilySymbolIds())
                    {
                        familySymbol = doc.GetElement(symbolId) as FamilySymbol;
                        break;
                    }
                    
                    if (familySymbol == null)
                    {
                        message = "Nenhum símbolo encontrado na família";
                        trans.RollBack();
                        return Result.Failed;
                    }
                    
                    // Ativar o símbolo da família se necessário
                    if (!familySymbol.IsActive)
                    {
                        familySymbol.Activate();
                    }
                    
                    // Inserir instância da família na origem
                    XYZ insertionPoint = new XYZ(0, 0, 0);
                    FamilyInstance instance = doc.Create.NewFamilyInstance(
                        insertionPoint, 
                        familySymbol, 
                        StructuralType.NonStructural
                    );
                    
                    if (instance == null)
                    {
                        message = "Falha ao criar instância da família";
                        trans.RollBack();
                        return Result.Failed;
                    }
                    
                    trans.Commit();
                }
                
                // Salvar o documento
                SaveAsOptions saveOptions = new SaveAsOptions();
                saveOptions.OverwriteExistingFile = true;
                doc.SaveAs(outputPath, saveOptions);
                
                // Exportar para IFC
                ExportToIFC(doc, ifcOutputPath);
                
                // Fechar o documento
                doc.Close(false);
                
                TaskDialog.Show("Sucesso", 
                    $"Modelo criado com sucesso!\n" +
                    $"Arquivo Revit: {outputPath}\n" +
                    $"Arquivo IFC: {ifcOutputPath}");
                
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                message = $"Erro: {ex.Message}";
                return Result.Failed;
            }
        }
        
        private void ExportToIFC(Document doc, string ifcPath)
        {
            // Configurar opções de exportação IFC
            IFCExportOptions ifcOptions = new IFCExportOptions();
            ifcOptions.FileVersion = IFCVersion.IFC2x3CV2;
            ifcOptions.SpaceBoundaryLevel = 1;
            ifcOptions.ExportBaseQuantities = true;
            ifcOptions.SplitWallsAndColumns = false;
            ifcOptions.VisibleElementsOfCurrentView = false;
            ifcOptions.Use2DRoomBoundaryForVolume = false;
            ifcOptions.UseFamilyAndTypeNameForReference = true;
            ifcOptions.ExportInternalRevitPropertySets = false;
            ifcOptions.ExportIFCCommonPropertySets = true;
            ifcOptions.Export2DElements = false;
            ifcOptions.ExportPartsAsBuildingElements = false;
            ifcOptions.ExportBoundingBox = false;
            ifcOptions.ExportSolidModelRep = false;
            ifcOptions.ExportSchedulesAsTables = false;
            ifcOptions.ExportUserDefinedPsets = false;
            ifcOptions.ExportUserDefinedPsetsFileName = "";
            ifcOptions.ExportLinkedFiles = false;
            ifcOptions.IncludeSiteElevation = false;
            ifcOptions.UseActiveViewGeometry = false;
            ifcOptions.ExportSpecificSchedules = false;
            ifcOptions.TessellationLevelOfDetail = 0.5;
            ifcOptions.StoreIFCGUID = true;
            
            // Exportar
            doc.Export(Path.GetDirectoryName(ifcPath), Path.GetFileName(ifcPath), ifcOptions);
        }
    }
}
